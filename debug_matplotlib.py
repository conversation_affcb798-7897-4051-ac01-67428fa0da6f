#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
matplotlib显示问题诊断脚本
"""

import sys
import os

print("=== matplotlib显示问题诊断 ===")
print(f"Python版本: {sys.version}")
print(f"操作系统: {os.name}")

# 检查matplotlib
try:
    import matplotlib
    print(f"matplotlib版本: {matplotlib.__version__}")
    print(f"当前后端: {matplotlib.get_backend()}")
    
    # 列出可用后端
    print("可用后端:")
    for backend in matplotlib.backend_bases._Backend.__subclasses__():
        print(f"  - {backend.__name__}")
    
    # 检查GUI后端
    gui_backends = ['TkAgg', 'Qt5Agg', 'Qt4Agg', 'GTKAgg']
    print("\nGUI后端检查:")
    for backend in gui_backends:
        try:
            matplotlib.use(backend, force=True)
            import matplotlib.pyplot as plt
            print(f"  ✓ {backend} - 可用")
        except Exception as e:
            print(f"  ✗ {backend} - 不可用: {e}")
    
    # 重置为TkAgg
    matplotlib.use('TkAgg', force=True)
    
except ImportError as e:
    print(f"matplotlib导入失败: {e}")

# 检查tkinter
try:
    import tkinter as tk
    print(f"tkinter版本: {tk.TkVersion}")
    
    # 测试tkinter窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏窗口
    print("tkinter正常工作")
    root.destroy()
    
except Exception as e:
    print(f"tkinter问题: {e}")

# 检查numpy
try:
    import numpy as np
    print(f"numpy版本: {np.__version__}")
except ImportError as e:
    print(f"numpy导入失败: {e}")

print("\n=== 测试简单绘图 ===")
try:
    import matplotlib
    matplotlib.use('TkAgg', force=True)
    import matplotlib.pyplot as plt
    import numpy as np
    
    # 创建测试图形
    fig, ax = plt.subplots(figsize=(8, 6))
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)
    ax.plot(x, y, 'b-', linewidth=2, label='sin(x)')
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_title('matplotlib测试图形')
    ax.legend()
    ax.grid(True)
    
    # 保存图形
    plt.savefig('matplotlib_test.png', dpi=150, bbox_inches='tight')
    print("测试图形已保存为 matplotlib_test.png")
    
    # 尝试显示
    plt.show(block=False)
    print("如果看到弹出窗口，说明matplotlib显示正常")
    
    plt.close()
    
except Exception as e:
    print(f"绘图测试失败: {e}")
    import traceback
    traceback.print_exc()