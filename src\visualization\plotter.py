#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化绘图模块
使用matplotlib实现2D/3D可视化显示功能
"""

import matplotlib
matplotlib.use('TkAgg')  # 确保使用TkAgg后端
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
from typing import List, Dict, Any, Tuple
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, Ellipse
import matplotlib.colors as mcolors

# 设置字体支持
try:
    import matplotlib.font_manager as fm
    # 获取系统中文字体
    chinese_fonts = []
    for font in fm.fontManager.ttflist:
        if 'SimHei' in font.name or 'Microsoft YaHei' in font.name or 'SimSun' in font.name:
            chinese_fonts.append(font.name)
    
    if chinese_fonts:
        plt.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans']
    else:
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
except:
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

plt.rcParams['axes.unicode_minus'] = False

class CoilPlotter:
    """线圈可视化绘图器"""
    
    def __init__(self):
        self.fig = None
        self.ax = None
        self.colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
    
    def plot_2d_layout(self, coil_data: List[Dict[str, Any]], mold_params: Dict[str, float] = None) -> plt.Figure:
        """绘制2D布置图"""
        self.fig, self.ax = plt.subplots(1, 1, figsize=(12, 8))

        # 绘制线圈
        for i, strip_data in enumerate(coil_data):
            color = self.colors[i % len(self.colors)]
            self._plot_2d_strip(strip_data, color, i)

        # 绘制接线块
        self._plot_terminal_blocks_2d(coil_data)

        # 绘制模具边界
        if mold_params:
            self._plot_mold_boundary_2d(mold_params)

        self.ax.set_xlabel('长度 (mm)')
        self.ax.set_ylabel('高度 (mm)')
        self.ax.set_title('2D Coil Layout')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_aspect('equal')
        self.ax.legend()

        plt.tight_layout()
        return self.fig
    
    def _plot_2d_strip(self, strip_data: Dict[str, Any], color: str, strip_index: int):
        """绘制单根铜带的2D视图 - 分匝显示"""
        # 优先使用新的分匝数据结构
        if 'turns' in strip_data and strip_data['turns']:
            self._plot_2d_strip_by_turns(strip_data, color, strip_index)
        else:
            # 回退到旧的方法
            self._plot_2d_strip_legacy(strip_data, color, strip_index)

    def _plot_2d_strip_by_turns(self, strip_data: Dict[str, Any], color: str, strip_index: int):
        """使用分匝数据绘制2D视图"""
        turns_data = strip_data['turns']

        # 为不同匝使用不同的颜色深度
        base_colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        base_color = base_colors[strip_index % len(base_colors)]

        for turn_idx, turn_data in enumerate(turns_data):
            coordinates = turn_data['coordinates']
            if not coordinates:
                continue

            x_coords = [coord[0] for coord in coordinates]
            y_coords = [coord[1] for coord in coordinates]

            # 闭合路径（确保矩形闭合）
            if len(x_coords) > 0 and (x_coords[0] != x_coords[-1] or y_coords[0] != y_coords[-1]):
                x_coords.append(x_coords[0])
                y_coords.append(y_coords[0])

            # 为每匝使用不同的样式
            if turn_idx == 0:
                # 内层匝：实线，较粗，较深色
                linestyle = '-'
                linewidth = 4
                alpha = 1.0
                turn_color = base_color
                label = f'Strip {strip_index + 1}'
            elif turn_idx == 1:
                # 中层匝：虚线，中等粗细
                linestyle = '--'
                linewidth = 3
                alpha = 0.8
                turn_color = base_color
                label = None
            else:
                # 外层匝：点线，较细
                linestyle = ':'
                linewidth = 2
                alpha = 0.6
                turn_color = base_color
                label = None

            self.ax.plot(x_coords, y_coords, color=turn_color, linewidth=linewidth,
                        linestyle=linestyle, label=label, alpha=alpha)

            # 标记第一匝的起点
            if turn_idx == 0 and x_coords and y_coords:
                self.ax.plot(x_coords[0], y_coords[0], 'o', color=turn_color, markersize=8)

    def _plot_2d_strip_legacy(self, strip_data: Dict[str, Any], color: str, strip_index: int):
        """旧版本的绘制方法（兼容性）"""
        coordinates = strip_data['coordinates']
        if not coordinates:
            return

        # 计算每匝的坐标点数
        points_per_turn = self._estimate_points_per_turn(coordinates)
        turns_count = max(1, len(coordinates) // points_per_turn)

        # 分匝绘制
        for turn in range(turns_count):
            start_idx = turn * points_per_turn
            end_idx = min((turn + 1) * points_per_turn, len(coordinates))

            if start_idx >= len(coordinates):
                break

            # 提取当前匝的坐标
            turn_coords = coordinates[start_idx:end_idx]
            if not turn_coords:
                continue

            x_coords = [coord[0] for coord in turn_coords]
            y_coords = [coord[1] for coord in turn_coords]

            # 闭合路径
            if len(x_coords) > 0 and (x_coords[0] != x_coords[-1] or y_coords[0] != y_coords[-1]):
                x_coords.append(x_coords[0])
                y_coords.append(y_coords[0])

            # 绘制当前匝
            alpha = 0.9 - turn * 0.15
            alpha = max(alpha, 0.4)
            linewidth = 3 - turn * 0.3
            linewidth = max(linewidth, 1.5)

            label = f'Strip {strip_index + 1}' if turn == 0 else None
            self.ax.plot(x_coords, y_coords, color=color, linewidth=linewidth,
                        label=label, alpha=alpha)

            # 标记第一匝的起点
            if turn == 0 and x_coords and y_coords:
                self.ax.plot(x_coords[0], y_coords[0], 'o', color=color, markersize=8)

    def _estimate_points_per_turn(self, coordinates):
        """估算每匝的坐标点数"""
        # 简单的启发式方法：假设坐标按匝顺序排列
        # 寻找坐标中的重复模式或距离突变来判断匝的分界
        if len(coordinates) < 8:  # 至少需要8个点才能形成一个基本矩形
            return len(coordinates)

        # 计算相邻点之间的距离
        distances = []
        for i in range(1, len(coordinates)):
            dx = coordinates[i][0] - coordinates[i-1][0]
            dy = coordinates[i][1] - coordinates[i-1][1]
            dist = (dx*dx + dy*dy)**0.5
            distances.append(dist)

        # 寻找距离的大跳跃，这通常表示从一匝的结束到下一匝的开始
        if not distances:
            return len(coordinates)

        avg_dist = sum(distances) / len(distances)
        large_jumps = []

        for i, dist in enumerate(distances):
            if dist > avg_dist * 3:  # 距离超过平均值3倍认为是大跳跃
                large_jumps.append(i + 1)  # +1因为distances比coordinates少1个

        if large_jumps:
            # 第一个大跳跃的位置就是第一匝的结束
            return large_jumps[0]
        else:
            # 如果没有明显的跳跃，假设所有点属于一匝
            return len(coordinates)

    def _plot_terminal_blocks_2d(self, coil_data: List[Dict[str, Any]]):
        """绘制接线块"""
        if not coil_data:
            return

        # 找到线圈的边界来确定接线块位置
        all_x = []
        all_y = []

        for strip_data in coil_data:
            coordinates = strip_data['coordinates']
            for coord in coordinates:
                all_x.append(coord[0])
                all_y.append(coord[1])

        if not all_x or not all_y:
            return

        min_x, max_x = min(all_x), max(all_x)
        min_y, max_y = min(all_y), max(all_y)

        # 接线块尺寸
        block_width = 20
        block_height = 15

        # 左上角接线块
        left_block_x = min_x - block_width/2
        left_block_y = max_y + 10
        left_rect = Rectangle((left_block_x, left_block_y), block_width, block_height,
                             linewidth=2, edgecolor='red', facecolor='lightcoral',
                             label='Terminal Block')
        self.ax.add_patch(left_rect)

        # 右上角接线块
        right_block_x = max_x - block_width/2
        right_block_y = max_y + 10
        right_rect = Rectangle((right_block_x, right_block_y), block_width, block_height,
                              linewidth=2, edgecolor='red', facecolor='lightcoral')
        self.ax.add_patch(right_rect)

        # 添加连接线（从线圈到接线块）
        # 左侧连接线
        self.ax.plot([min_x, left_block_x + block_width/2],
                    [max_y, left_block_y],
                    'r-', linewidth=2, alpha=0.7)

        # 右侧连接线
        self.ax.plot([max_x, right_block_x + block_width/2],
                    [max_y, right_block_y],
                    'r-', linewidth=2, alpha=0.7)
    
    def _plot_mold_boundary_2d(self, mold_params: Dict[str, float]):
        """绘制模具边界"""
        length_limit = mold_params.get('length_limit', 0)
        height_limit = mold_params.get('height_limit', 0)
        
        if length_limit > 0 and height_limit > 0:
            # 绘制模具边界矩形
            rect = Rectangle((-length_limit/2, -height_limit/2), 
                           length_limit, height_limit,
                           linewidth=3, edgecolor='black', 
                           facecolor='none', linestyle='--',
                           label='模具边界')
            self.ax.add_patch(rect)
    
    def plot_3d_layout(self, coil_data: List[Dict[str, Any]], mold_params: Dict[str, float] = None) -> plt.Figure:
        """绘制3D布置图"""
        self.fig = plt.figure(figsize=(14, 10))
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # 绘制线圈
        for i, strip_data in enumerate(coil_data):
            color = self.colors[i % len(self.colors)]
            self._plot_3d_strip(strip_data, color, i)
        
        # 绘制模具边界
        if mold_params:
            self._plot_mold_boundary_3d(mold_params)
        
        self.ax.set_xlabel('长度 (mm)')
        self.ax.set_ylabel('高度 (mm)')
        self.ax.set_zlabel('宽度 (mm)')
        self.ax.set_title('电流互感器线圈3D布置图')
        self.ax.legend()
        
        return self.fig
    
    def _plot_3d_strip(self, strip_data: Dict[str, Any], color: str, strip_index: int):
        """绘制单根铜带的3D视图"""
        coordinates = strip_data['coordinates']
        width = strip_data['dimensions']['width']
        
        if not coordinates:
            return
        
        # 提取坐标
        x_coords = [coord[0] for coord in coordinates]
        y_coords = [coord[1] for coord in coordinates]
        z_coords = [coord[2] for coord in coordinates]
        
        # 绘制中心线
        self.ax.plot(x_coords, y_coords, z_coords, color=color, linewidth=2, 
                    label=f'铜带 {strip_index + 1}', alpha=0.8)
        
        # 绘制铜带的厚度表示（简化为线条）
        z_top = [z + width/2 for z in z_coords]
        z_bottom = [z - width/2 for z in z_coords]
        
        self.ax.plot(x_coords, y_coords, z_top, color=color, linewidth=1, alpha=0.5)
        self.ax.plot(x_coords, y_coords, z_bottom, color=color, linewidth=1, alpha=0.5)
    
    def _plot_mold_boundary_3d(self, mold_params: Dict[str, float]):
        """绘制3D模具边界"""
        length_limit = mold_params.get('length_limit', 0)
        height_limit = mold_params.get('height_limit', 0)
        width_limit = mold_params.get('width_limit', 0)
        
        if length_limit > 0 and height_limit > 0 and width_limit > 0:
            # 绘制模具边界框架
            self._draw_3d_box(length_limit, height_limit, width_limit)
    
    def _draw_3d_box(self, length: float, height: float, width: float):
        """绘制3D边界框"""
        # 定义边界框的8个顶点
        x = [-length/2, length/2, length/2, -length/2, -length/2, length/2, length/2, -length/2]
        y = [-height/2, -height/2, height/2, height/2, -height/2, -height/2, height/2, height/2]
        z = [-width/2, -width/2, -width/2, -width/2, width/2, width/2, width/2, width/2]
        
        # 绘制边界框的边
        edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
            [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
            [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
        ]
        
        for edge in edges:
            points = np.array([[x[edge[0]], y[edge[0]], z[edge[0]]], 
                              [x[edge[1]], y[edge[1]], z[edge[1]]]])
            self.ax.plot3D(points[:, 0], points[:, 1], points[:, 2], 
                          'k--', linewidth=2, alpha=0.6)
    
    def plot_cross_section(self, coil_data: List[Dict[str, Any]], section_type: str = 'xy') -> plt.Figure:
        """绘制截面图"""
        self.fig, self.ax = plt.subplots(1, 1, figsize=(10, 8))
        
        for i, strip_data in enumerate(coil_data):
            color = self.colors[i % len(self.colors)]
            self._plot_cross_section_strip(strip_data, color, i, section_type)
        
        if section_type == 'xy':
            self.ax.set_xlabel('长度 (mm)')
            self.ax.set_ylabel('高度 (mm)')
            self.ax.set_title('XY截面图')
        elif section_type == 'xz':
            self.ax.set_xlabel('长度 (mm)')
            self.ax.set_ylabel('宽度 (mm)')
            self.ax.set_title('XZ截面图')
        elif section_type == 'yz':
            self.ax.set_xlabel('高度 (mm)')
            self.ax.set_ylabel('宽度 (mm)')
            self.ax.set_title('YZ截面图')
        
        self.ax.grid(True, alpha=0.3)
        self.ax.set_aspect('equal')
        self.ax.legend()
        
        plt.tight_layout()
        return self.fig
    
    def _plot_cross_section_strip(self, strip_data: Dict[str, Any], color: str, 
                                 strip_index: int, section_type: str):
        """绘制截面铜带"""
        coordinates = strip_data['coordinates']
        if not coordinates:
            return
        
        if section_type == 'xy':
            coords_1 = [coord[0] for coord in coordinates]  # x
            coords_2 = [coord[1] for coord in coordinates]  # y
        elif section_type == 'xz':
            coords_1 = [coord[0] for coord in coordinates]  # x
            coords_2 = [coord[2] for coord in coordinates]  # z
        elif section_type == 'yz':
            coords_1 = [coord[1] for coord in coordinates]  # y
            coords_2 = [coord[2] for coord in coordinates]  # z
        else:
            return
        
        self.ax.plot(coords_1, coords_2, color=color, linewidth=2, 
                    label=f'铜带 {strip_index + 1}', alpha=0.7)
    
    def plot_interference_analysis(self, coil_data: List[Dict[str, Any]], 
                                 mold_params: Dict[str, float],
                                 interference_data: Dict[str, Any]) -> plt.Figure:
        """绘制干涉分析图"""
        self.fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 左图：2D布置图带干涉标识
        self._plot_interference_2d(ax1, coil_data, mold_params, interference_data)
        
        # 右图：间隙分析
        self._plot_clearance_analysis(ax2, interference_data)
        
        plt.tight_layout()
        return self.fig
    
    def _plot_interference_2d(self, ax, coil_data: List[Dict[str, Any]], 
                             mold_params: Dict[str, float], 
                             interference_data: Dict[str, Any]):
        """绘制带干涉标识的2D图"""
        # 绘制线圈
        for i, strip_data in enumerate(coil_data):
            color = self.colors[i % len(self.colors)]
            coordinates = strip_data['coordinates']
            if coordinates:
                x_coords = [coord[0] for coord in coordinates]
                y_coords = [coord[1] for coord in coordinates]
                ax.plot(x_coords, y_coords, color=color, linewidth=2, 
                       label=f'铜带 {i + 1}', alpha=0.7)
        
        # 绘制模具边界
        if mold_params:
            length_limit = mold_params.get('length_limit', 0)
            height_limit = mold_params.get('height_limit', 0)
            
            if length_limit > 0 and height_limit > 0:
                rect_color = 'red' if interference_data.get('has_interference', False) else 'green'
                rect = Rectangle((-length_limit/2, -height_limit/2), 
                               length_limit, height_limit,
                               linewidth=3, edgecolor=rect_color, 
                               facecolor='none', linestyle='--',
                               label='模具边界')
                ax.add_patch(rect)
        
        ax.set_xlabel('长度 (mm)')
        ax.set_ylabel('高度 (mm)')
        ax.set_title('干涉检测图')
        ax.grid(True, alpha=0.3)
        ax.set_aspect('equal')
        ax.legend()
    
    def _plot_clearance_analysis(self, ax, interference_data: Dict[str, Any]):
        """绘制间隙分析图"""
        clearance_info = interference_data.get('clearance_info', {})
        
        if not clearance_info:
            ax.text(0.5, 0.5, '无间隙数据', ha='center', va='center', transform=ax.transAxes)
            return
        
        # 准备数据
        directions = ['长度方向', '高度方向', '宽度方向']
        clearances = [
            clearance_info.get('length_clearance', 0),
            clearance_info.get('height_clearance', 0),
            clearance_info.get('width_clearance', 0)
        ]
        
        # 颜色编码：负值为红色，正值为绿色
        colors = ['red' if c < 0 else 'green' for c in clearances]
        
        # 绘制柱状图
        bars = ax.bar(directions, clearances, color=colors, alpha=0.7)
        
        # 添加数值标签
        for bar, clearance in zip(bars, clearances):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -1),
                   f'{clearance:.2f}mm', ha='center', va='bottom' if height >= 0 else 'top')
        
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.set_ylabel('间隙 (mm)')
        ax.set_title('间隙分析')
        ax.grid(True, alpha=0.3)
        
        # 添加浇注层厚度信息
        casting_thickness = clearance_info.get('min_casting_thickness', 0)
        ax.text(0.02, 0.98, f'最小浇注层厚度: {casting_thickness:.2f}mm', 
               transform=ax.transAxes, va='top', 
               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def save_plot(self, filename: str, dpi: int = 300):
        """保存图形"""
        if self.fig:
            self.fig.savefig(filename, dpi=dpi, bbox_inches='tight')
    
    def close_plot(self):
        """关闭图形"""
        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
