#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证工具模块
提供参数验证和干涉检测的辅助功能
"""

import math
from typing import Dict, List, Tuple, Any

class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def validate_coil_parameters(params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证线圈参数
        返回: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查必需参数
        required_params = ['thickness', 'width', 'count', 'turns', 'inner_length', 'inner_height']
        for param in required_params:
            if param not in params:
                errors.append(f"缺少必需参数: {param}")
                continue
            
            value = params[param]
            if not isinstance(value, (int, float)) or value <= 0:
                errors.append(f"参数 {param} 必须是大于0的数值")
        
        if errors:
            return False, errors
        
        # 检查参数合理性
        thickness = params['thickness']
        width = params['width']
        count = params['count']
        turns = params['turns']
        inner_length = params['inner_length']
        inner_height = params['inner_height']
        
        # 厚度合理性检查
        if thickness > 10:
            errors.append("铜带厚度过大，建议小于10mm")
        elif thickness < 0.1:
            errors.append("铜带厚度过小，建议大于0.1mm")
        
        # 宽度合理性检查
        if width > 200:
            errors.append("铜带宽度过大，建议小于200mm")
        elif width < 5:
            errors.append("铜带宽度过小，建议大于5mm")
        
        # 根数合理性检查
        if count > 50:
            errors.append("铜带根数过多，建议小于50根")
        
        # 匝数合理性检查
        if turns > 100:
            errors.append("匝数过多，建议小于100匝")
        
        # 内径尺寸合理性检查
        if inner_length < inner_height * 0.5:
            errors.append("复绕内径长度与高度比例不合理，建议长度至少为高度的0.5倍")
        
        if inner_length > inner_height * 10:
            errors.append("复绕内径长度与高度比例不合理，建议长度不超过高度的10倍")
        
        # 总体积检查
        estimated_volume = ValidationUtils.estimate_copper_volume(params)
        if estimated_volume > 1000:  # 1000 cm³
            errors.append(f"估算铜带总体积过大({estimated_volume:.1f} cm³)，建议减少参数")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_mold_parameters(params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证模具参数
        返回: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查必需参数
        required_params = ['length_limit', 'height_limit', 'width_limit']
        for param in required_params:
            if param not in params:
                errors.append(f"缺少必需参数: {param}")
                continue
            
            value = params[param]
            if not isinstance(value, (int, float)) or value <= 0:
                errors.append(f"参数 {param} 必须是大于0的数值")
        
        if errors:
            return False, errors
        
        # 检查参数合理性
        length_limit = params['length_limit']
        height_limit = params['height_limit']
        width_limit = params['width_limit']
        
        # 尺寸合理性检查
        if length_limit > 1000:
            errors.append("模具长度限制过大，建议小于1000mm")
        elif length_limit < 10:
            errors.append("模具长度限制过小，建议大于10mm")
        
        if height_limit > 1000:
            errors.append("模具高度限制过大，建议小于1000mm")
        elif height_limit < 10:
            errors.append("模具高度限制过小，建议大于10mm")
        
        if width_limit > 500:
            errors.append("模具宽度限制过大，建议小于500mm")
        elif width_limit < 5:
            errors.append("模具宽度限制过小，建议大于5mm")
        
        # 比例检查
        aspect_ratio_lh = length_limit / height_limit
        if aspect_ratio_lh > 20 or aspect_ratio_lh < 0.05:
            errors.append("模具长度与高度比例不合理")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_compatibility(coil_params: Dict[str, Any], mold_params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证线圈参数与模具参数的兼容性
        返回: (是否兼容, 警告信息列表)
        """
        warnings = []
        
        try:
            # 估算线圈最大外径
            max_dims = ValidationUtils.estimate_max_dimensions(coil_params)
            
            # 检查是否超出模具限制
            length_margin = mold_params['length_limit'] - max_dims['length']
            height_margin = mold_params['height_limit'] - max_dims['height']
            width_margin = mold_params['width_limit'] - max_dims['width']
            
            if length_margin < 0:
                warnings.append(f"线圈长度超出模具限制 {abs(length_margin):.2f}mm")
            elif length_margin < 10:
                warnings.append(f"线圈长度余量较小({length_margin:.2f}mm)，建议增加模具尺寸或减少线圈参数")
            
            if height_margin < 0:
                warnings.append(f"线圈高度超出模具限制 {abs(height_margin):.2f}mm")
            elif height_margin < 10:
                warnings.append(f"线圈高度余量较小({height_margin:.2f}mm)，建议增加模具尺寸或减少线圈参数")
            
            if width_margin < 0:
                warnings.append(f"线圈宽度超出模具限制 {abs(width_margin):.2f}mm")
            elif width_margin < 5:
                warnings.append(f"线圈宽度余量较小({width_margin:.2f}mm)，建议增加模具尺寸")
            
            # 检查浇注层厚度
            min_casting_thickness = min(length_margin, height_margin, width_margin) / 2
            if min_casting_thickness > 0 and min_casting_thickness < 5:
                warnings.append(f"浇注层厚度过小({min_casting_thickness:.2f}mm)，建议至少5mm")
            
            # 检查材料利用率
            coil_volume = ValidationUtils.estimate_copper_volume(coil_params)
            mold_volume = mold_params['length_limit'] * mold_params['height_limit'] * mold_params['width_limit'] / 1000
            utilization = coil_volume / mold_volume * 100
            
            if utilization < 10:
                warnings.append(f"材料利用率较低({utilization:.1f}%)，建议优化设计")
            elif utilization > 80:
                warnings.append(f"材料利用率过高({utilization:.1f}%)，可能影响浇注质量")
            
        except Exception as e:
            warnings.append(f"兼容性检查时出错: {str(e)}")
        
        return len([w for w in warnings if "超出模具限制" in w]) == 0, warnings
    
    @staticmethod
    def estimate_max_dimensions(coil_params: Dict[str, Any]) -> Dict[str, float]:
        """估算线圈最大外径尺寸"""
        thickness = coil_params['thickness']
        width = coil_params['width']
        count = coil_params['count']
        turns = coil_params['turns']
        inner_length = coil_params['inner_length']
        inner_height = coil_params['inner_height']
        
        # 计算径向增加量
        radial_increase = (count - 1 + turns) * thickness
        
        # 计算最大外径
        max_length = inner_length + 2 * radial_increase
        max_height = inner_height + 2 * radial_increase
        max_width = width
        
        return {
            'length': max_length,
            'height': max_height,
            'width': max_width
        }
    
    @staticmethod
    def estimate_copper_volume(coil_params: Dict[str, Any]) -> float:
        """估算铜带总体积 (cm³)"""
        thickness = coil_params['thickness']
        width = coil_params['width']
        count = coil_params['count']
        turns = coil_params['turns']
        inner_length = coil_params['inner_length']
        inner_height = coil_params['inner_height']
        
        # 估算每匝的平均周长（椭圆周长近似）
        avg_a = inner_length / 2 + (count - 1 + turns) * thickness / 2
        avg_b = inner_height / 2 + (count - 1 + turns) * thickness / 2
        
        # 椭圆周长近似公式
        perimeter = math.pi * (3 * (avg_a + avg_b) - math.sqrt((3 * avg_a + avg_b) * (avg_a + 3 * avg_b)))
        
        # 总长度
        total_length = perimeter * turns * count
        
        # 体积 = 长度 × 宽度 × 厚度
        volume = total_length * width * thickness / 1000  # 转换为 cm³
        
        return volume
    
    @staticmethod
    def generate_optimization_suggestions(coil_params: Dict[str, Any], 
                                        mold_params: Dict[str, Any],
                                        interference_data: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # 基于干涉检测结果的建议
        if interference_data.get('has_interference', False):
            clearance_info = interference_data.get('clearance_info', {})
            
            if clearance_info.get('length_clearance', 0) < 0:
                suggestions.append("减少铜带根数或匝数以降低长度方向尺寸")
                suggestions.append("增加模具长度限制")
            
            if clearance_info.get('height_clearance', 0) < 0:
                suggestions.append("减少铜带根数或匝数以降低高度方向尺寸")
                suggestions.append("增加模具高度限制")
            
            if clearance_info.get('width_clearance', 0) < 0:
                suggestions.append("减少铜带宽度")
                suggestions.append("增加模具宽度限制")
        
        # 基于参数分析的建议
        max_dims = ValidationUtils.estimate_max_dimensions(coil_params)
        
        # 效率优化建议
        if coil_params['count'] > 10:
            suggestions.append("考虑减少铜带根数，使用更厚的铜带以提高效率")
        
        if coil_params['turns'] > 20:
            suggestions.append("考虑减少匝数，优化电气性能")
        
        # 制造工艺建议
        if coil_params['thickness'] < 1:
            suggestions.append("铜带厚度较小，注意制造工艺的可行性")
        
        if coil_params['width'] / coil_params['thickness'] > 50:
            suggestions.append("铜带宽厚比过大，可能影响复绕质量")
        
        # 浇注工艺建议
        min_casting = interference_data.get('clearance_info', {}).get('min_casting_thickness', 0)
        if 0 < min_casting < 5:
            suggestions.append("增加浇注层厚度以确保浇注质量")
        
        # 去重并返回
        return list(set(suggestions))
    
    @staticmethod
    def check_manufacturing_feasibility(coil_params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """检查制造可行性"""
        issues = []
        
        thickness = coil_params['thickness']
        width = coil_params['width']
        count = coil_params['count']
        turns = coil_params['turns']
        
        # 铜带规格检查
        if thickness < 0.5:
            issues.append("铜带厚度过小，可能难以制造和处理")
        
        if width / thickness > 100:
            issues.append("铜带宽厚比过大，复绕时容易变形")
        
        # 复绕工艺检查
        if count * turns > 200:
            issues.append("总复绕长度过大，可能影响制造效率")
        
        # 空间紧密度检查
        total_thickness = count * thickness
        inner_perimeter = math.pi * (coil_params['inner_length'] + coil_params['inner_height']) / 2
        if total_thickness > inner_perimeter / 10:
            issues.append("铜带在内径处过于紧密，可能影响复绕质量")
        
        return len(issues) == 0, issues
