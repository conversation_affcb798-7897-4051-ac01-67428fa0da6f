# 电流互感器线圈布置模拟软件 v1.0

## 软件简介

本软件是专为电流互感器设计开发的线圈布置模拟工具，能够根据输入的铜带规格参数和模具尺寸参数，自动计算并生成铜带复绕后的三维布置图，同时提供干涉检测和优化建议功能。

## 主要功能

### 1. 输入参数模块
- **铜带规格参数**：
  - 铜带厚度（mm）
  - 铜带宽度（mm）
  - 铜带根数
  - 匝数
  - 复绕内径的长度和高度（mm）

### 2. 核心计算模块
- 根据输入参数自动计算铜带复绕后的三维布置
- 生成每根铜带的空间坐标数据
- 计算线圈的最大外径尺寸
- 估算铜带总体积

### 3. 可视化显示模块
- **2D布置图**：显示线圈的平面布置情况
- **3D布置图**：显示线圈的三维空间分布
- **截面图**：显示不同方向的截面视图
- **干涉分析图**：可视化显示与模具的位置关系

### 4. 验证检查模块
- **模具尺寸输入**：设置模具的长度、高度、宽度限制
- **干涉检测**：自动检测铜带布置与模具边界的干涉情况
- **浇注层分析**：计算并显示浇注层厚度空间
- **优化建议**：根据计算结果提供设计优化建议

## 技术特点

- **开发语言**：Python 3.x
- **图形界面**：Tkinter
- **可视化引擎**：Matplotlib
- **数值计算**：NumPy, SciPy
- **实时预览**：参数调整后自动更新显示
- **多视图显示**：支持2D/3D多角度查看

## 安装和运行

### 环境要求
- Python 3.7 或更高版本
- 推荐使用 Windows 10/11 系统

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行软件
```bash
python main.py
```

## 使用说明

### 1. 启动软件
运行 `main.py` 启动软件主界面。

### 2. 输入参数
在左侧参数面板中输入：
- 铜带规格参数（厚度、宽度、根数、匝数、复绕内径）
- 模具尺寸参数（长度、高度、宽度限制）

### 3. 计算布置
点击"计算布置"按钮开始计算，结果将显示在左下方的结果区域。

### 4. 查看图形
在右侧标签页中查看：
- **2D布置图**：线圈的平面布置
- **3D布置图**：线圈的三维空间布置
- **干涉分析**：与模具的干涉检测结果
- **截面图**：线圈的截面视图

### 5. 参数调整
根据计算结果和优化建议调整参数，软件支持实时预览。

## 文件结构

```
线圈布置模拟1.0/
├── main.py                 # 主启动程序
├── requirements.txt        # 依赖包列表
├── README.md              # 说明文档
├── test_core_functions.py # 功能测试脚本
└── src/                   # 源代码目录
    ├── core/              # 核心计算模块
    │   └── coil_calculator.py
    ├── gui/               # 图形界面模块
    │   ├── main_window.py
    │   └── parameter_panel.py
    ├── visualization/     # 可视化模块
    │   └── plotter.py
    └── utils/             # 工具模块
        └── validation.py
```

## 功能测试

运行测试脚本验证软件功能：
```bash
python test_core_functions.py
```

测试将验证：
- 线圈计算器功能
- 参数验证功能
- 可视化功能
- 多参数组合测试

## 使用示例

### 典型参数设置
- **小型线圈**：厚度1mm，宽度30mm，3根2匝，内径50×30mm
- **中型线圈**：厚度2mm，宽度50mm，3根5匝，内径100×60mm
- **大型线圈**：厚度3mm，宽度80mm，5根8匝，内径150×100mm

### 模具设置
根据实际模具尺寸设置长度、高度、宽度限制，软件会自动检测干涉并给出优化建议。

## 注意事项

1. **参数合理性**：确保输入参数在合理范围内
2. **单位统一**：所有长度单位均为毫米（mm）
3. **浇注层厚度**：建议保持至少5mm的浇注层厚度
4. **制造工艺**：考虑实际制造工艺的可行性

## 常见问题

### Q: 软件显示中文乱码怎么办？
A: 这是matplotlib字体问题，不影响功能使用，图形和数据计算都是正常的。

### Q: 如何保存计算结果？
A: 使用"文件"菜单中的"导出图形"功能可以保存所有图形到指定文件夹。

### Q: 参数输入后没有反应？
A: 请点击"计算布置"按钮手动触发计算，或检查参数是否在合理范围内。

### Q: 如何优化设计？
A: 查看"干涉分析"标签页和结果区域的优化建议，根据提示调整参数。

## 版本信息

- **当前版本**：v1.0
- **发布日期**：2024年
- **开发环境**：Python 3.x + Tkinter + Matplotlib

## 技术支持

如有技术问题或功能建议，请通过以下方式联系：
- 查看软件内置帮助文档
- 运行测试脚本验证功能
- 检查README文档中的常见问题解答

---

**电流互感器线圈布置模拟软件** - 为电流互感器设计提供专业的可视化模拟工具
