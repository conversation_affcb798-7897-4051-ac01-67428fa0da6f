#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线圈计算模块
实现铜带复绕计算算法，生成三维布置坐标数据
"""

import numpy as np
import math
from typing import List, Tuple, Dict, Any

class CoilParameters:
    """线圈参数类"""
    def __init__(self):
        self.thickness = 0.0      # 铜带厚度 (mm)
        self.width = 0.0          # 铜带宽度 (mm)
        self.count = 1            # 铜带根数
        self.turns = 1            # 匝数
        self.inner_length = 0.0   # 复绕内径长度 (mm)
        self.inner_height = 0.0   # 复绕内径高度 (mm)

class MoldParameters:
    """模具参数类"""
    def __init__(self):
        self.length_limit = 0.0   # 模具长度限制 (mm)
        self.height_limit = 0.0   # 模具高度限制 (mm)
        self.width_limit = 0.0    # 模具宽度限制 (mm)

class CoilCalculator:
    """线圈计算器"""
    
    def __init__(self):
        self.coil_params = CoilParameters()
        self.mold_params = MoldParameters()
        self.coil_coordinates = []
        self.interference_data = {}
    
    def set_coil_parameters(self, thickness: float, width: float, count: int, 
                           turns: int, inner_length: float, inner_height: float):
        """设置线圈参数"""
        self.coil_params.thickness = thickness
        self.coil_params.width = width
        self.coil_params.count = count
        self.coil_params.turns = turns
        self.coil_params.inner_length = inner_length
        self.coil_params.inner_height = inner_height
    
    def set_mold_parameters(self, length_limit: float, height_limit: float, width_limit: float):
        """设置模具参数"""
        self.mold_params.length_limit = length_limit
        self.mold_params.height_limit = height_limit
        self.mold_params.width_limit = width_limit
    
    def calculate_coil_layout(self) -> List[Dict[str, Any]]:
        """
        计算线圈布置
        返回每根铜带的三维坐标数据
        """
        coil_data = []
        
        # 计算每根铜带的位置
        for strip_idx in range(self.coil_params.count):
            strip_data = self._calculate_single_strip_layout(strip_idx)
            coil_data.append(strip_data)
        
        self.coil_coordinates = coil_data
        return coil_data
    
    def _calculate_single_strip_layout(self, strip_index: int) -> Dict[str, Any]:
        """计算单根铜带的布置"""
        strip_data = {
            'strip_id': strip_index,
            'coordinates': [],  # 所有匝的坐标（用于兼容性）
            'turns': [],        # 每匝的坐标分开存储
            'dimensions': {
                'thickness': self.coil_params.thickness,
                'width': self.coil_params.width
            }
        }

        # 计算铜带在复绕中的径向位置
        radial_position = strip_index * self.coil_params.thickness

        # 计算每匝的坐标
        for turn in range(self.coil_params.turns):
            turn_coords = self._calculate_turn_coordinates(turn, radial_position)

            # 分别存储每匝的坐标
            strip_data['turns'].append({
                'turn_number': turn,
                'coordinates': turn_coords
            })

            # 同时保持原有的连续坐标格式（用于兼容性）
            strip_data['coordinates'].extend(turn_coords)

        return strip_data
    
    def _calculate_turn_coordinates(self, turn_number: int, radial_offset: float) -> List[Tuple[float, float, float]]:
        """计算单匝的坐标点 - 矩形路径"""
        coordinates = []
        
        # 基础内径尺寸（矩形）
        inner_length = self.coil_params.inner_length  # 内径长度
        inner_height = self.coil_params.inner_height  # 内径高度
        
        # 考虑匝数增加的径向偏移（包括纸板间隔）
        paper_thickness = 0.5  # 纸板厚度 0.5mm
        turn_offset = turn_number * (self.coil_params.thickness + paper_thickness)
        
        # 当前匝的尺寸
        current_length = inner_length + 2 * (radial_offset + turn_offset)
        current_height = inner_height + 2 * (radial_offset + turn_offset)
        
        # 圆角半径（避免尖角）
        corner_radius = min(10, (radial_offset + turn_offset) * 0.5)
        
        # 生成矩形路径坐标点
        coordinates = self._generate_rounded_rectangle_path(
            current_length, current_height, corner_radius
        )
        
        return coordinates
    
    def _generate_rounded_rectangle_path(self, length: float, height: float, corner_radius: float) -> List[Tuple[float, float, float]]:
        """生成圆角矩形路径坐标"""
        coordinates = []
        
        # 矩形的半长和半高
        half_length = length / 2
        half_height = height / 2
        
        # 确保圆角半径不超过矩形尺寸
        max_radius = min(half_length, half_height) * 0.9
        corner_radius = min(corner_radius, max_radius)
        
        # 每段的点数
        straight_points = 25  # 直线段点数
        corner_points = 15    # 圆角段点数
        
        # 右边直线段（从下到上）
        for i in range(straight_points):
            t = i / (straight_points - 1)
            x = half_length - corner_radius
            y = -half_height + corner_radius + t * (height - 2 * corner_radius)
            coordinates.append((x, y, 0))
        
        # 右上圆角
        for i in range(corner_points):
            angle = i / (corner_points - 1) * math.pi / 2
            x = half_length - corner_radius + corner_radius * math.cos(angle)
            y = half_height - corner_radius + corner_radius * math.sin(angle)
            coordinates.append((x, y, 0))
        
        # 上边直线段（从右到左）
        for i in range(straight_points):
            t = i / (straight_points - 1)
            x = half_length - corner_radius - t * (length - 2 * corner_radius)
            y = half_height - corner_radius
            coordinates.append((x, y, 0))
        
        # 左上圆角
        for i in range(corner_points):
            angle = math.pi / 2 + i / (corner_points - 1) * math.pi / 2
            x = -half_length + corner_radius + corner_radius * math.cos(angle)
            y = half_height - corner_radius + corner_radius * math.sin(angle)
            coordinates.append((x, y, 0))
        
        # 左边直线段（从上到下）
        for i in range(straight_points):
            t = i / (straight_points - 1)
            x = -half_length + corner_radius
            y = half_height - corner_radius - t * (height - 2 * corner_radius)
            coordinates.append((x, y, 0))
        
        # 左下圆角
        for i in range(corner_points):
            angle = math.pi + i / (corner_points - 1) * math.pi / 2
            x = -half_length + corner_radius + corner_radius * math.cos(angle)
            y = -half_height + corner_radius + corner_radius * math.sin(angle)
            coordinates.append((x, y, 0))
        
        # 下边直线段（从左到右）
        for i in range(straight_points):
            t = i / (straight_points - 1)
            x = -half_length + corner_radius + t * (length - 2 * corner_radius)
            y = -half_height + corner_radius
            coordinates.append((x, y, 0))
        
        # 右下圆角
        for i in range(corner_points):
            angle = 3 * math.pi / 2 + i / (corner_points - 1) * math.pi / 2
            x = half_length - corner_radius + corner_radius * math.cos(angle)
            y = -half_height + corner_radius + corner_radius * math.sin(angle)
            coordinates.append((x, y, 0))
        
        return coordinates
    
    def check_interference(self) -> Dict[str, Any]:
        """检查干涉情况"""
        if not self.coil_coordinates:
            return {'has_interference': False, 'message': '请先计算线圈布置'}
        
        interference_result = {
            'has_interference': False,
            'interference_details': [],
            'clearance_info': {},
            'recommendations': []
        }
        
        # 计算最大外径尺寸
        max_outer_dimensions = self._calculate_max_outer_dimensions()
        
        # 检查与模具的干涉
        length_clearance = self.mold_params.length_limit - max_outer_dimensions['length']
        height_clearance = self.mold_params.height_limit - max_outer_dimensions['height']
        width_clearance = self.mold_params.width_limit - max_outer_dimensions['width']
        
        interference_result['clearance_info'] = {
            'length_clearance': length_clearance,
            'height_clearance': height_clearance,
            'width_clearance': width_clearance,
            'min_casting_thickness': min(length_clearance, height_clearance, width_clearance) / 2
        }
        
        # 判断是否有干涉
        if length_clearance < 0 or height_clearance < 0 or width_clearance < 0:
            interference_result['has_interference'] = True
            
            if length_clearance < 0:
                interference_result['interference_details'].append(
                    f'长度方向超出模具限制 {abs(length_clearance):.2f}mm'
                )
            if height_clearance < 0:
                interference_result['interference_details'].append(
                    f'高度方向超出模具限制 {abs(height_clearance):.2f}mm'
                )
            if width_clearance < 0:
                interference_result['interference_details'].append(
                    f'宽度方向超出模具限制 {abs(width_clearance):.2f}mm'
                )
        
        # 生成建议
        if interference_result['has_interference']:
            interference_result['recommendations'].append('建议减少铜带根数或匝数')
            interference_result['recommendations'].append('建议减小铜带厚度')
            interference_result['recommendations'].append('建议使用更大的模具')
        else:
            min_clearance = min(length_clearance, height_clearance, width_clearance)
            if min_clearance < 10:  # 浇注层厚度小于10mm时给出警告
                interference_result['recommendations'].append(
                    f'浇注层厚度较小({min_clearance/2:.2f}mm)，建议优化设计'
                )
        
        self.interference_data = interference_result
        return interference_result
    
    def _calculate_max_outer_dimensions(self) -> Dict[str, float]:
        """计算线圈的最大外径尺寸"""
        if not self.coil_coordinates:
            return {'length': 0, 'height': 0, 'width': 0}
        
        # 纸板厚度
        paper_thickness = 0.5  # mm
        
        # 计算最外层的径向厚度
        # 每根铜带的径向厚度 + 每匝之间的纸板厚度
        strip_radial_thickness = self.coil_params.turns * (self.coil_params.thickness + paper_thickness)
        # 减去最后一层不需要纸板
        strip_radial_thickness -= paper_thickness
        
        # 多根铜带之间的间隔（假设为铜带厚度）
        total_radial_thickness = self.coil_params.count * strip_radial_thickness + (self.coil_params.count - 1) * self.coil_params.thickness
        
        max_length = self.coil_params.inner_length + 2 * total_radial_thickness
        max_height = self.coil_params.inner_height + 2 * total_radial_thickness
        max_width = self.coil_params.width
        
        return {
            'length': max_length,
            'height': max_height,
            'width': max_width
        }
    
    def get_coil_summary(self) -> Dict[str, Any]:
        """获取线圈布置摘要信息"""
        if not self.coil_coordinates:
            return {}
        
        max_dims = self._calculate_max_outer_dimensions()
        
        return {
            'coil_parameters': {
                'thickness': self.coil_params.thickness,
                'width': self.coil_params.width,
                'count': self.coil_params.count,
                'turns': self.coil_params.turns,
                'inner_length': self.coil_params.inner_length,
                'inner_height': self.coil_params.inner_height
            },
            'calculated_dimensions': max_dims,
            'total_copper_volume': self._calculate_copper_volume(),
            'strip_count': len(self.coil_coordinates)
        }
    
    def _calculate_copper_volume(self) -> float:
        """计算铜带总体积"""
        # 纸板厚度
        paper_thickness = 0.5  # mm
        
        total_volume = 0
        
        # 计算每根铜带每匝的体积
        for strip_idx in range(self.coil_params.count):
            strip_radial_offset = strip_idx * (self.coil_params.thickness + paper_thickness)
            
            for turn in range(self.coil_params.turns):
                turn_radial_offset = turn * (self.coil_params.thickness + paper_thickness)
                total_radial_offset = strip_radial_offset + turn_radial_offset
                
                # 当前匝的矩形尺寸
                current_length = self.coil_params.inner_length + 2 * total_radial_offset
                current_height = self.coil_params.inner_height + 2 * total_radial_offset
                
                # 矩形周长（简化，不考虑圆角）
                perimeter = 2 * (current_length + current_height)
                
                # 当前匝的体积
                turn_volume = perimeter * self.coil_params.width * self.coil_params.thickness
                total_volume += turn_volume
        
        return total_volume / 1000  # 转换为立方厘米
