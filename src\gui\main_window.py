#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
电流互感器线圈布置模拟软件的主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 尝试导入matplotlib，如果失败则使用备用方案
try:
    import matplotlib
    # 尝试不同的后端，优先使用TkAgg
    backends_to_try = ['TkAgg', 'Qt5Agg', 'Agg']
    backend_success = False
    
    for backend in backends_to_try:
        try:
            matplotlib.use(backend, force=True)
            backend_success = True
            print(f"成功使用matplotlib后端: {backend}")
            break
        except Exception as be:
            print(f"后端 {backend} 不可用: {be}")
            continue
    
    if not backend_success:
        raise Exception("所有matplotlib后端都不可用")
    
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk

    # 设置matplotlib中文字体和参数
    try:
        # 尝试设置中文字体
        import matplotlib.font_manager as fm
        # 获取系统中文字体
        chinese_fonts = []
        for font in fm.fontManager.ttflist:
            if 'SimHei' in font.name or 'Microsoft YaHei' in font.name or 'SimSun' in font.name:
                chinese_fonts.append(font.name)
        
        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans']
        else:
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.dpi'] = 100
        plt.rcParams['savefig.dpi'] = 150
        print(f"字体设置: {plt.rcParams['font.sans-serif']}")
    except Exception as fe:
        print(f"字体设置失败: {fe}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    MATPLOTLIB_AVAILABLE = True
    print("matplotlib加载成功")
except Exception as e:
    print(f"matplotlib加载失败: {e}")
    MATPLOTLIB_AVAILABLE = False

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.gui.parameter_panel import ParameterPanel
from src.core.coil_calculator import CoilCalculator
from src.visualization.plotter import CoilPlotter

class MainWindow:
    """主窗口类"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        
        # 初始化核心组件
        self.calculator = CoilCalculator()
        self.plotter = CoilPlotter()
        
        # 当前计算结果
        self.current_coil_data = []
        self.current_interference_data = {}
        
        self.setup_ui()
        self.setup_menu()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("电流互感器线圈布置模拟软件 v1.0")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 可以添加图标文件
            pass
        except:
            pass
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主要的PanedWindow
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板：参数输入和控制
        self.left_frame = ttk.Frame(self.main_paned, width=400)
        self.main_paned.add(self.left_frame, weight=0)
        
        # 右侧面板：可视化显示
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=1)
        
        # 设置左侧面板
        self.setup_left_panel()
        
        # 设置右侧面板
        self.setup_right_panel()
    
    def setup_left_panel(self):
        """设置左侧参数面板"""
        # 参数输入面板
        self.parameter_panel = ParameterPanel(self.left_frame, self.on_parameter_change)
        
        # 添加结果显示区域
        self.setup_result_display()
    
    def setup_result_display(self):
        """设置结果显示区域"""
        # 结果显示框架
        result_frame = ttk.LabelFrame(self.left_frame, text="计算结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(10, 0))
        
        # 创建文本显示区域
        self.result_text = tk.Text(result_frame, height=15, wrap=tk.WORD, 
                                  font=('Consolas', 9))
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始显示
        self.update_result_display("请输入参数并点击'计算布置'按钮开始计算...")
    
    def setup_right_panel(self):
        """设置右侧可视化面板"""
        # 创建Notebook用于多标签页显示
        self.notebook = ttk.Notebook(self.right_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 2D布置图标签页
        self.tab_2d = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_2d, text="2D布置图")
        
        # 3D布置图标签页
        self.tab_3d = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_3d, text="3D布置图")
        
        # 干涉分析标签页
        self.tab_interference = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_interference, text="干涉分析")
        
        # 截面图标签页
        self.tab_section = ttk.Frame(self.notebook)
        self.notebook.add(self.tab_section, text="截面图")
        
        # 初始化各个标签页
        self.setup_plot_tabs()
    
    def setup_plot_tabs(self):
        """设置绘图标签页"""
        self.canvases = {}
        self.toolbars = {}

        tabs = {
            '2d': self.tab_2d,
            '3d': self.tab_3d,
            'interference': self.tab_interference,
            'section': self.tab_section
        }

        if MATPLOTLIB_AVAILABLE:
            # 使用matplotlib
            for tab_name, tab_frame in tabs.items():
                try:
                    # 创建matplotlib图形，使用更小的DPI避免显示问题
                    fig = plt.Figure(figsize=(10, 8), dpi=80)
                    fig.patch.set_facecolor('white')

                    # 创建画布
                    canvas = FigureCanvasTkAgg(fig, tab_frame)
                    canvas_widget = canvas.get_tk_widget()
                    canvas_widget.pack(fill=tk.BOTH, expand=True)

                    # 创建工具栏框架
                    toolbar_frame = ttk.Frame(tab_frame)
                    toolbar_frame.pack(side=tk.BOTTOM, fill=tk.X)
                    
                    # 创建工具栏
                    toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
                    toolbar.update()

                    self.canvases[tab_name] = canvas
                    self.toolbars[tab_name] = toolbar

                    # 初始显示提示信息
                    ax = fig.add_subplot(111)
                    ax.text(0.5, 0.5, f'Please calculate coil layout first\nThen view {tab_name.upper()} graphics',
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=12, alpha=0.6, color='gray')
                    ax.set_xticks([])
                    ax.set_yticks([])
                    ax.spines['top'].set_visible(False)
                    ax.spines['right'].set_visible(False)
                    ax.spines['bottom'].set_visible(False)
                    ax.spines['left'].set_visible(False)
                    
                    # 强制刷新画布
                    canvas.draw()
                    canvas.flush_events()
                    
                    # 确保画布可见
                    canvas_widget.update()
                    canvas_widget.update_idletasks()
                    
                    print(f"成功创建{tab_name}标签页")

                except Exception as e:
                    print(f"创建{tab_name}标签页失败: {e}")
                    import traceback
                    traceback.print_exc()
                    self._create_fallback_tab(tab_name, tab_frame)
        else:
            # 使用备用方案
            for tab_name, tab_frame in tabs.items():
                self._create_fallback_tab(tab_name, tab_frame)

    def _create_fallback_tab(self, tab_name, tab_frame):
        """创建备用标签页（当matplotlib不可用时）"""
        # 创建文本显示区域
        text_frame = ttk.Frame(tab_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 添加说明文本
        info_text = tk.Text(text_frame, wrap=tk.WORD, font=('Arial', 12))
        info_text.pack(fill=tk.BOTH, expand=True)

        fallback_message = f"""
{tab_name.upper()}图形显示区域

matplotlib图形库不可用，无法显示图形。

计算结果将以文本形式显示在左侧结果区域中。

要启用图形显示功能，请确保：
1. 已正确安装matplotlib库
2. 系统支持图形界面显示
3. Python环境配置正确

您仍然可以使用所有计算功能，
只是无法查看可视化图形。
        """

        info_text.insert(tk.END, fallback_message)
        info_text.config(state=tk.DISABLED)

        # 保存引用（虽然不是真正的canvas，但保持接口一致）
        self.canvases[tab_name] = None
        self.toolbars[tab_name] = None

        print(f"创建{tab_name}备用标签页")
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="新建项目", command=self.new_project)
        file_menu.add_separator()
        file_menu.add_command(label="保存参数", command=self.parameter_panel.save_parameters)
        file_menu.add_command(label="加载参数", command=self.parameter_panel.load_parameters)
        file_menu.add_separator()
        file_menu.add_command(label="导出图形", command=self.export_plots)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 视图菜单
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="视图", menu=view_menu)
        view_menu.add_command(label="刷新显示", command=self.refresh_display)
        view_menu.add_command(label="重置视图", command=self.reset_view)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="参数验证", command=self.validate_all_parameters)
        tools_menu.add_command(label="优化建议", command=self.show_optimization_suggestions)
        tools_menu.add_separator()
        tools_menu.add_command(label="测试图形显示", command=self.test_plot_display)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def on_parameter_change(self, params, force_calculate=False):
        """参数变化回调函数"""
        try:
            # 设置计算器参数
            coil_params = params['coil']
            mold_params = params['mold']
            
            self.calculator.set_coil_parameters(
                coil_params['thickness'],
                coil_params['width'],
                coil_params['count'],
                coil_params['turns'],
                coil_params['inner_length'],
                coil_params['inner_height']
            )
            
            self.calculator.set_mold_parameters(
                mold_params['length_limit'],
                mold_params['height_limit'],
                mold_params['width_limit']
            )
            
            # 如果是强制计算或者参数有显著变化，则重新计算
            if force_calculate:
                self.calculate_and_display()
        
        except Exception as e:
            self.update_result_display(f"参数设置错误：{str(e)}")
    
    def calculate_and_display(self):
        """计算并显示结果"""
        try:
            # 计算线圈布置
            self.current_coil_data = self.calculator.calculate_coil_layout()
            
            # 检查干涉
            self.current_interference_data = self.calculator.check_interference()
            
            # 更新结果显示
            self.update_result_display()
            
            # 更新图形显示
            self.update_plots()

            # 强制刷新界面
            self.root.update_idletasks()
            
        except Exception as e:
            messagebox.showerror("计算错误", f"计算过程中出现错误：{str(e)}")
            self.update_result_display(f"计算错误：{str(e)}")
    
    def update_result_display(self, message=None):
        """更新结果显示"""
        self.result_text.delete(1.0, tk.END)
        
        if message:
            self.result_text.insert(tk.END, message)
            return
        
        if not self.current_coil_data:
            self.result_text.insert(tk.END, "暂无计算结果")
            return
        
        # 显示计算摘要
        summary = self.calculator.get_coil_summary()
        
        result_text = "=== 线圈布置计算结果 ===\n\n"
        
        # 输入参数
        result_text += "输入参数：\n"
        coil_params = summary['coil_parameters']
        result_text += f"  铜带厚度: {coil_params['thickness']:.2f} mm\n"
        result_text += f"  铜带宽度: {coil_params['width']:.2f} mm\n"
        result_text += f"  铜带根数: {coil_params['count']}\n"
        result_text += f"  匝数: {coil_params['turns']}\n"
        result_text += f"  复绕内径: {coil_params['inner_length']:.2f} × {coil_params['inner_height']:.2f} mm\n\n"
        
        # 计算结果
        result_text += "计算结果：\n"
        calc_dims = summary['calculated_dimensions']
        result_text += f"  最大外径尺寸: {calc_dims['length']:.2f} × {calc_dims['height']:.2f} × {calc_dims['width']:.2f} mm\n"
        result_text += f"  铜带总体积: {summary['total_copper_volume']:.2f} cm³\n"
        result_text += f"  铜带总数: {summary['strip_count']} 根\n\n"
        
        # 干涉检查结果
        result_text += "干涉检查：\n"
        if self.current_interference_data.get('has_interference', False):
            result_text += "  ⚠️ 发现干涉问题！\n"
            for detail in self.current_interference_data.get('interference_details', []):
                result_text += f"    - {detail}\n"
        else:
            result_text += "  ✅ 无干涉问题\n"
        
        clearance_info = self.current_interference_data.get('clearance_info', {})
        if clearance_info:
            result_text += f"  浇注层厚度: {clearance_info.get('min_casting_thickness', 0):.2f} mm\n"
        
        # 建议
        recommendations = self.current_interference_data.get('recommendations', [])
        if recommendations:
            result_text += "\n建议：\n"
            for rec in recommendations:
                result_text += f"  • {rec}\n"
        
        self.result_text.insert(tk.END, result_text)
    
    def update_plots(self):
        """更新所有图形显示"""
        if not self.current_coil_data:
            print("没有线圈数据，跳过图形更新")
            return

        print("开始更新图形显示...")

        if not MATPLOTLIB_AVAILABLE:
            print("matplotlib不可用，跳过图形更新")
            return

        try:
            mold_params = {
                'length_limit': self.calculator.mold_params.length_limit,
                'height_limit': self.calculator.mold_params.height_limit,
                'width_limit': self.calculator.mold_params.width_limit
            }

            print(f"模具参数: {mold_params}")
            print(f"线圈数据数量: {len(self.current_coil_data)}")

            # 更新2D图
            print("更新2D图...")
            self.update_2d_plot(mold_params)

            # 更新3D图
            print("更新3D图...")
            self.update_3d_plot(mold_params)

            # 更新干涉分析图
            print("更新干涉分析图...")
            self.update_interference_plot(mold_params)

            # 更新截面图
            print("更新截面图...")
            self.update_section_plot()

            # 强制刷新所有画布
            for canvas in self.canvases.values():
                if canvas:
                    canvas.draw_idle()
                    canvas.flush_events()
                    canvas.get_tk_widget().update()
                    canvas.get_tk_widget().update_idletasks()

            print("图形更新完成！")

        except Exception as e:
            print(f"更新图形时出错：{str(e)}")
            import traceback
            traceback.print_exc()
    
    def update_2d_plot(self, mold_params):
        """更新2D图"""
        canvas = self.canvases.get('2d')
        if not canvas or not MATPLOTLIB_AVAILABLE:
            print("2D画布不可用，跳过更新")
            return

        try:
            # 清除图形
            canvas.figure.clear()
            
            # 创建子图
            ax = canvas.figure.add_subplot(111)
            
            # 绘制线圈数据
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
            for i, strip_data in enumerate(self.current_coil_data):
                coordinates = strip_data['coordinates']
                if coordinates:
                    color = colors[i % len(colors)]
                    x_coords = [coord[0] for coord in coordinates]
                    y_coords = [coord[1] for coord in coordinates]
                    ax.plot(x_coords, y_coords, color=color, linewidth=2, 
                           label=f'Strip {i + 1}', alpha=0.7)
                    # 标记起点
                    if x_coords and y_coords:
                        ax.plot(x_coords[0], y_coords[0], 'o', color=color, markersize=6)
            
            # 绘制内径矩形
            inner_length = self.calculator.coil_params.inner_length
            inner_height = self.calculator.coil_params.inner_height
            from matplotlib.patches import Rectangle
            inner_rect = Rectangle((-inner_length/2, -inner_height/2), 
                                 inner_length, inner_height,
                                 linewidth=2, edgecolor='gray', 
                                 facecolor='lightgray', alpha=0.3,
                                 label='Inner Core')
            ax.add_patch(inner_rect)
            
            # 绘制接线块（左上角和右上角）
            terminal_width = 20
            terminal_height = 15
            # 左上角接线块
            left_terminal = Rectangle((-inner_length/2 - terminal_width, inner_height/2), 
                                    terminal_width, terminal_height,
                                    linewidth=2, edgecolor='red', 
                                    facecolor='lightcoral', alpha=0.7,
                                    label='Terminal Block')
            ax.add_patch(left_terminal)
            
            # 右上角接线块
            right_terminal = Rectangle((inner_length/2, inner_height/2), 
                                     terminal_width, terminal_height,
                                     linewidth=2, edgecolor='red', 
                                     facecolor='lightcoral', alpha=0.7)
            ax.add_patch(right_terminal)
            
            # 绘制模具边界
            if mold_params and mold_params.get('length_limit', 0) > 0:
                length_limit = mold_params['length_limit']
                height_limit = mold_params['height_limit']
                mold_rect = Rectangle((-length_limit/2, -height_limit/2), 
                                    length_limit, height_limit,
                                    linewidth=3, edgecolor='black', 
                                    facecolor='none', linestyle='--',
                                    label='Mold Boundary')
                ax.add_patch(mold_rect)
            
            ax.set_xlabel('Length (mm)')
            ax.set_ylabel('Height (mm)')
            ax.set_title('2D Coil Layout')
            ax.grid(True, alpha=0.3)
            ax.set_aspect('equal')
            ax.legend()
            
            # 强制刷新
            canvas.figure.tight_layout()
            canvas.draw()
            canvas.flush_events()
            print("2D图更新成功")
        except Exception as e:
            print(f"2D图更新失败: {e}")
            import traceback
            traceback.print_exc()
    
    def update_3d_plot(self, mold_params):
        """更新3D图"""
        canvas = self.canvases.get('3d')
        if not canvas or not MATPLOTLIB_AVAILABLE:
            print("3D画布不可用，跳过更新")
            return

        try:
            # 清除图形
            canvas.figure.clear()
            
            # 创建3D子图
            ax = canvas.figure.add_subplot(111, projection='3d')
            
            # 绘制线圈数据
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
            for i, strip_data in enumerate(self.current_coil_data):
                coordinates = strip_data['coordinates']
                width = strip_data['dimensions']['width']
                
                if coordinates:
                    color = colors[i % len(colors)]
                    x_coords = [coord[0] for coord in coordinates]
                    y_coords = [coord[1] for coord in coordinates]
                    z_coords = [coord[2] for coord in coordinates]
                    
                    # 绘制中心线
                    ax.plot(x_coords, y_coords, z_coords, color=color, linewidth=2, 
                           label=f'Strip {i + 1}', alpha=0.8)
                    
                    # 绘制铜带的厚度表示
                    z_top = [z + width/2 for z in z_coords]
                    z_bottom = [z - width/2 for z in z_coords]
                    ax.plot(x_coords, y_coords, z_top, color=color, linewidth=1, alpha=0.5)
                    ax.plot(x_coords, y_coords, z_bottom, color=color, linewidth=1, alpha=0.5)
            
            # 绘制3D模具边界
            if mold_params and all(mold_params.get(k, 0) > 0 for k in ['length_limit', 'height_limit', 'width_limit']):
                self._draw_3d_box_simple(ax, mold_params)
            
            ax.set_xlabel('Length (mm)')
            ax.set_ylabel('Height (mm)')
            ax.set_zlabel('Width (mm)')
            ax.set_title('3D Coil Layout')
            ax.legend()
            
            # 强制刷新
            canvas.draw()
            canvas.flush_events()
            print("3D图更新成功")
        except Exception as e:
            print(f"3D图更新失败: {e}")
            import traceback
            traceback.print_exc()
    
    def update_interference_plot(self, mold_params):
        """更新干涉分析图"""
        canvas = self.canvases['interference']
        if not canvas:
            return
            
        try:
            canvas.figure.clear()
            
            # 创建两个子图
            ax1 = canvas.figure.add_subplot(121)
            ax2 = canvas.figure.add_subplot(122)
            
            # 左图：2D布置图带干涉标识
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
            for i, strip_data in enumerate(self.current_coil_data):
                coordinates = strip_data['coordinates']
                if coordinates:
                    color = colors[i % len(colors)]
                    x_coords = [coord[0] for coord in coordinates]
                    y_coords = [coord[1] for coord in coordinates]
                    ax1.plot(x_coords, y_coords, color=color, linewidth=2, 
                            label=f'Strip {i + 1}', alpha=0.7)
            
            # 绘制模具边界
            if mold_params and mold_params.get('length_limit', 0) > 0:
                length_limit = mold_params['length_limit']
                height_limit = mold_params['height_limit']
                rect_color = 'red' if self.current_interference_data.get('has_interference', False) else 'green'
                from matplotlib.patches import Rectangle
                rect = Rectangle((-length_limit/2, -height_limit/2), 
                               length_limit, height_limit,
                               linewidth=3, edgecolor=rect_color, 
                               facecolor='none', linestyle='--',
                               label='Mold Boundary')
                ax1.add_patch(rect)
            
            ax1.set_xlabel('Length (mm)')
            ax1.set_ylabel('Height (mm)')
            ax1.set_title('Interference Detection')
            ax1.grid(True, alpha=0.3)
            ax1.set_aspect('equal')
            ax1.legend()
            
            # 右图：间隙分析
            clearance_info = self.current_interference_data.get('clearance_info', {})
            if clearance_info:
                directions = ['Length', 'Height', 'Width']
                clearances = [
                    clearance_info.get('length_clearance', 0),
                    clearance_info.get('height_clearance', 0),
                    clearance_info.get('width_clearance', 0)
                ]
                
                # 颜色编码：负值为红色，正值为绿色
                bar_colors = ['red' if c < 0 else 'green' for c in clearances]
                
                # 绘制柱状图
                bars = ax2.bar(directions, clearances, color=bar_colors, alpha=0.7)
                
                # 添加数值标签
                for bar, clearance in zip(bars, clearances):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -1),
                           f'{clearance:.2f}mm', ha='center', va='bottom' if height >= 0 else 'top')
                
                ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
                ax2.set_ylabel('Clearance (mm)')
                ax2.set_title('Clearance Analysis')
                ax2.grid(True, alpha=0.3)
                
                # 添加浇注层厚度信息
                casting_thickness = clearance_info.get('min_casting_thickness', 0)
                ax2.text(0.02, 0.98, f'Min casting thickness: {casting_thickness:.2f}mm', 
                        transform=ax2.transAxes, va='top', 
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            
            canvas.figure.tight_layout()
            canvas.draw()
            canvas.flush_events()
        except Exception as e:
            print(f"干涉分析图更新失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _draw_3d_box_simple(self, ax, mold_params):
        """绘制简单的3D边界框"""
        try:
            import numpy as np
            length = mold_params['length_limit']
            height = mold_params['height_limit']
            width = mold_params['width_limit']
            
            # 定义边界框的8个顶点
            x = [-length/2, length/2, length/2, -length/2, -length/2, length/2, length/2, -length/2]
            y = [-height/2, -height/2, height/2, height/2, -height/2, -height/2, height/2, height/2]
            z = [-width/2, -width/2, -width/2, -width/2, width/2, width/2, width/2, width/2]
            
            # 绘制边界框的边
            edges = [
                [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
                [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
                [0, 4], [1, 5], [2, 6], [3, 7]   # 竖直边
            ]
            
            for edge in edges:
                points = np.array([[x[edge[0]], y[edge[0]], z[edge[0]]], 
                                  [x[edge[1]], y[edge[1]], z[edge[1]]]])
                ax.plot3D(points[:, 0], points[:, 1], points[:, 2], 
                         'k--', linewidth=2, alpha=0.6)
        except Exception as e:
            print(f"绘制3D边界框失败: {e}")

    def update_section_plot(self):
        """更新截面图"""
        canvas = self.canvases['section']
        if not canvas:
            return
            
        try:
            canvas.figure.clear()
            
            # 创建子图
            ax = canvas.figure.add_subplot(111)
            
            # 绘制XY截面
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
            for i, strip_data in enumerate(self.current_coil_data):
                coordinates = strip_data['coordinates']
                if coordinates:
                    color = colors[i % len(colors)]
                    x_coords = [coord[0] for coord in coordinates]  # x
                    y_coords = [coord[1] for coord in coordinates]  # y
                    ax.plot(x_coords, y_coords, color=color, linewidth=2, 
                           label=f'Strip {i + 1}', alpha=0.7)
            
            ax.set_xlabel('Length (mm)')
            ax.set_ylabel('Height (mm)')
            ax.set_title('XY Cross Section')
            ax.grid(True, alpha=0.3)
            ax.set_aspect('equal')
            ax.legend()
            
            canvas.figure.tight_layout()
            canvas.draw()
            canvas.flush_events()
        except Exception as e:
            print(f"截面图更新失败: {e}")
    
    # 菜单回调函数
    def new_project(self):
        """新建项目"""
        self.parameter_panel.reset_parameters()
        self.current_coil_data = []
        self.current_interference_data = {}
        self.update_result_display("新项目已创建，请输入参数开始计算...")
        
        # 清空所有图形
        for canvas in self.canvases.values():
            canvas.figure.clear()
            ax = canvas.figure.add_subplot(111)
            ax.text(0.5, 0.5, '请先计算线圈布置', ha='center', va='center', 
                   transform=ax.transAxes, fontsize=14, alpha=0.6)
            ax.set_xticks([])
            ax.set_yticks([])
            canvas.draw()
    
    def export_plots(self):
        """导出图形"""
        if not self.current_coil_data:
            messagebox.showwarning("导出警告", "请先计算线圈布置再导出图形")
            return
        
        from tkinter import filedialog
        import os
        
        folder = filedialog.askdirectory(title="选择导出文件夹")
        if folder:
            try:
                for plot_type, canvas in self.canvases.items():
                    filename = os.path.join(folder, f"线圈布置_{plot_type}.png")
                    canvas.figure.savefig(filename, dpi=300, bbox_inches='tight')
                
                messagebox.showinfo("导出成功", f"图形已导出到 {folder}")
            except Exception as e:
                messagebox.showerror("导出失败", f"导出图形时出错：{str(e)}")
    
    def refresh_display(self):
        """刷新显示"""
        if self.current_coil_data:
            self.update_plots()
        else:
            # 如果没有数据，重新初始化图形
            for tab_name, canvas in self.canvases.items():
                canvas.figure.clear()
                ax = canvas.figure.add_subplot(111)
                ax.text(0.5, 0.5, f'请先计算线圈布置\n然后查看{tab_name.upper()}图形',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=14, alpha=0.6)
                ax.set_xticks([])
                ax.set_yticks([])
                canvas.draw()
                canvas.flush_events()
    
    def reset_view(self):
        """重置视图"""
        for toolbar in self.toolbars.values():
            toolbar.home()
    
    def validate_all_parameters(self):
        """验证所有参数"""
        try:
            params = self.parameter_panel.get_parameters()
            if self.parameter_panel.validate_parameters(params):
                messagebox.showinfo("验证结果", "所有参数验证通过！")
        except Exception as e:
            messagebox.showerror("验证失败", f"参数验证时出错：{str(e)}")
    
    def show_optimization_suggestions(self):
        """显示优化建议"""
        if not self.current_interference_data:
            messagebox.showinfo("优化建议", "请先计算线圈布置以获取优化建议")
            return
        
        suggestions = self.current_interference_data.get('recommendations', [])
        if suggestions:
            suggestion_text = "优化建议：\n\n" + "\n".join(f"• {s}" for s in suggestions)
        else:
            suggestion_text = "当前设计良好，暂无特殊优化建议。"
        
        messagebox.showinfo("优化建议", suggestion_text)
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
电流互感器线圈布置模拟软件使用说明：

1. 参数输入：
   - 在左侧面板输入铜带规格参数和模具尺寸参数
   - 参数会实时验证，确保输入有效

2. 计算布置：
   - 点击"计算布置"按钮开始计算
   - 计算结果会显示在左下方的结果区域

3. 查看图形：
   - 2D布置图：查看线圈的平面布置
   - 3D布置图：查看线圈的三维空间布置
   - 干涉分析：检查与模具的干涉情况
   - 截面图：查看线圈的截面视图

4. 文件操作：
   - 可以保存和加载参数配置
   - 可以导出计算结果图形

5. 其他功能：
   - 参数验证：检查输入参数的合理性
   - 优化建议：根据计算结果提供优化建议
        """
        
        # 创建帮助窗口
        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("600x500")
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
电流互感器线圈布置模拟软件 v1.0

开发目的：
为电流互感器设计提供线圈布置的可视化模拟和干涉检测功能

主要功能：
• 铜带复绕计算
• 三维布置可视化
• 模具干涉检测
• 浇注层厚度分析

技术特点：
• Python + Tkinter GUI
• Matplotlib 可视化
• 实时参数验证
• 多视图显示

版权信息：
© 2024 线圈布置模拟软件
        """
        messagebox.showinfo("关于软件", about_text)

    def test_plot_display(self):
        """测试图形显示功能"""
        print("开始测试图形显示...")

        try:
            import numpy as np

            # 在2D标签页显示测试图形
            canvas = self.canvases['2d']
            canvas.figure.clear()

            ax = canvas.figure.add_subplot(111)

            # 生成测试数据
            x = np.linspace(0, 2*np.pi, 100)
            y1 = np.sin(x)
            y2 = np.cos(x)

            # 绘制测试图形
            ax.plot(x, y1, 'r-', label='sin(x)', linewidth=2)
            ax.plot(x, y2, 'b-', label='cos(x)', linewidth=2)
            ax.set_xlabel('X轴')
            ax.set_ylabel('Y轴')
            ax.set_title('图形显示测试 - 如果能看到此图说明显示正常')
            ax.legend()
            ax.grid(True, alpha=0.3)

            canvas.draw()
            canvas.flush_events()

            print("测试图形已绘制到2D标签页")
            messagebox.showinfo("测试结果", "测试图形已显示在2D布置图标签页中\n如果能看到正弦余弦函数图形，说明显示系统正常工作")

        except Exception as e:
            print(f"测试图形显示时出错：{str(e)}")
            messagebox.showerror("测试失败", f"图形显示测试失败：{str(e)}")
