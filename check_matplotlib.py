#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查matplotlib配置
"""

import sys
print("Python版本:", sys.version)

try:
    import matplotlib
    print("matplotlib版本:", matplotlib.__version__)
    print("当前后端:", matplotlib.get_backend())
    
    # 尝试设置TkAgg后端
    matplotlib.use('TkAgg', force=True)
    print("设置后端为TkAgg")
    print("新后端:", matplotlib.get_backend())
    
    import matplotlib.pyplot as plt
    print("matplotlib.pyplot导入成功")
    
    import tkinter as tk
    print("tkinter导入成功")
    
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    print("FigureCanvasTkAgg导入成功")
    
    # 测试创建简单窗口
    root = tk.Tk()
    root.title("matplotlib测试")
    root.geometry("400x300")
    
    fig = plt.Figure(figsize=(4, 3))
    ax = fig.add_subplot(111)
    ax.plot([1, 2, 3, 4], [1, 4, 2, 3])
    ax.set_title("测试图形")
    
    canvas = FigureCanvasTkAgg(fig, root)
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    canvas.draw()
    
    print("测试窗口创建成功，应该能看到一个简单的折线图")
    print("如果看不到图形，可能是系统缺少必要的图形库")
    
    # 5秒后自动关闭
    root.after(5000, root.quit)
    root.mainloop()
    
except ImportError as e:
    print(f"导入错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
    import traceback
    traceback.print_exc()
