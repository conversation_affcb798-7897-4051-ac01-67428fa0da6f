#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数输入面板
提供铜带规格参数和模具参数的输入界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Callable, Any

class ParameterPanel:
    """参数输入面板类"""
    
    def __init__(self, parent, on_parameter_change: Callable = None):
        self.parent = parent
        self.on_parameter_change = on_parameter_change
        
        # 参数变量
        self.coil_vars = {}
        self.mold_vars = {}
        
        self.setup_ui()
        self.bind_events()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建铜带参数组
        self.create_coil_parameters_group()
        
        # 创建模具参数组
        self.create_mold_parameters_group()
        
        # 创建控制按钮
        self.create_control_buttons()
    
    def create_coil_parameters_group(self):
        """创建铜带参数输入组"""
        # 铜带参数框架
        coil_frame = ttk.LabelFrame(self.main_frame, text="铜带规格参数", padding=10)
        coil_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 铜带厚度
        ttk.Label(coil_frame, text="铜带厚度 (mm):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.coil_vars['thickness'] = tk.DoubleVar(value=2.0)
        thickness_entry = ttk.Entry(coil_frame, textvariable=self.coil_vars['thickness'], width=15)
        thickness_entry.grid(row=0, column=1, padx=(10, 0), pady=2)
        
        # 铜带宽度
        ttk.Label(coil_frame, text="铜带宽度 (mm):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.coil_vars['width'] = tk.DoubleVar(value=50.0)
        width_entry = ttk.Entry(coil_frame, textvariable=self.coil_vars['width'], width=15)
        width_entry.grid(row=1, column=1, padx=(10, 0), pady=2)
        
        # 铜带根数
        ttk.Label(coil_frame, text="铜带根数:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.coil_vars['count'] = tk.IntVar(value=1)
        count_spinbox = ttk.Spinbox(coil_frame, from_=1, to=20, textvariable=self.coil_vars['count'], width=13)
        count_spinbox.grid(row=2, column=1, padx=(10, 0), pady=2)
        
        # 匝数
        ttk.Label(coil_frame, text="匝数:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.coil_vars['turns'] = tk.IntVar(value=3)
        turns_spinbox = ttk.Spinbox(coil_frame, from_=1, to=50, textvariable=self.coil_vars['turns'], width=13)
        turns_spinbox.grid(row=3, column=1, padx=(10, 0), pady=2)
        
        # 复绕内径长度
        ttk.Label(coil_frame, text="复绕内径长度 (mm):").grid(row=0, column=2, sticky=tk.W, padx=(20, 0), pady=2)
        self.coil_vars['inner_length'] = tk.DoubleVar(value=200.0)
        inner_length_entry = ttk.Entry(coil_frame, textvariable=self.coil_vars['inner_length'], width=15)
        inner_length_entry.grid(row=0, column=3, padx=(10, 0), pady=2)
        
        # 复绕内径高度
        ttk.Label(coil_frame, text="复绕内径高度 (mm):").grid(row=1, column=2, sticky=tk.W, padx=(20, 0), pady=2)
        self.coil_vars['inner_height'] = tk.DoubleVar(value=85.0)
        inner_height_entry = ttk.Entry(coil_frame, textvariable=self.coil_vars['inner_height'], width=15)
        inner_height_entry.grid(row=1, column=3, padx=(10, 0), pady=2)
    
    def create_mold_parameters_group(self):
        """创建模具参数输入组"""
        # 模具参数框架
        mold_frame = ttk.LabelFrame(self.main_frame, text="模具尺寸参数", padding=10)
        mold_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 模具长度限制
        ttk.Label(mold_frame, text="模具长度限制 (mm):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.mold_vars['length_limit'] = tk.DoubleVar(value=300.0)
        length_entry = ttk.Entry(mold_frame, textvariable=self.mold_vars['length_limit'], width=15)
        length_entry.grid(row=0, column=1, padx=(10, 0), pady=2)
        
        # 模具高度限制
        ttk.Label(mold_frame, text="模具高度限制 (mm):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.mold_vars['height_limit'] = tk.DoubleVar(value=200.0)
        height_entry = ttk.Entry(mold_frame, textvariable=self.mold_vars['height_limit'], width=15)
        height_entry.grid(row=1, column=1, padx=(10, 0), pady=2)
        
        # 模具宽度限制
        ttk.Label(mold_frame, text="模具宽度限制 (mm):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.mold_vars['width_limit'] = tk.DoubleVar(value=80.0)
        width_entry = ttk.Entry(mold_frame, textvariable=self.mold_vars['width_limit'], width=15)
        width_entry.grid(row=2, column=1, padx=(10, 0), pady=2)
    
    def create_control_buttons(self):
        """创建控制按钮"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 计算按钮
        self.calculate_btn = ttk.Button(button_frame, text="计算布置", command=self.on_calculate)
        self.calculate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重置按钮
        self.reset_btn = ttk.Button(button_frame, text="重置参数", command=self.reset_parameters)
        self.reset_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存参数按钮
        self.save_btn = ttk.Button(button_frame, text="保存参数", command=self.save_parameters)
        self.save_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 加载参数按钮
        self.load_btn = ttk.Button(button_frame, text="加载参数", command=self.load_parameters)
        self.load_btn.pack(side=tk.LEFT)
    
    def bind_events(self):
        """绑定事件"""
        # 为所有参数变量绑定变化事件
        for var in self.coil_vars.values():
            var.trace('w', self.on_parameter_changed)
        
        for var in self.mold_vars.values():
            var.trace('w', self.on_parameter_changed)
    
    def on_parameter_changed(self, *args):
        """参数变化回调"""
        if self.on_parameter_change:
            try:
                params = self.get_parameters()
                self.on_parameter_change(params)
            except tk.TclError:
                # 忽略输入过程中的临时错误
                pass
    
    def on_calculate(self):
        """计算按钮回调"""
        try:
            params = self.get_parameters()
            if self.validate_parameters(params):
                if self.on_parameter_change:
                    self.on_parameter_change(params, force_calculate=True)
        except Exception as e:
            messagebox.showerror("参数错误", f"参数输入有误：{str(e)}")
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        coil_params = {
            'thickness': self.coil_vars['thickness'].get(),
            'width': self.coil_vars['width'].get(),
            'count': self.coil_vars['count'].get(),
            'turns': self.coil_vars['turns'].get(),
            'inner_length': self.coil_vars['inner_length'].get(),
            'inner_height': self.coil_vars['inner_height'].get()
        }
        
        mold_params = {
            'length_limit': self.mold_vars['length_limit'].get(),
            'height_limit': self.mold_vars['height_limit'].get(),
            'width_limit': self.mold_vars['width_limit'].get()
        }
        
        return {
            'coil': coil_params,
            'mold': mold_params
        }
    
    def validate_parameters(self, params: Dict[str, Any]) -> bool:
        """验证参数有效性"""
        coil = params['coil']
        mold = params['mold']
        
        # 检查铜带参数
        if coil['thickness'] <= 0:
            messagebox.showerror("参数错误", "铜带厚度必须大于0")
            return False
        
        if coil['width'] <= 0:
            messagebox.showerror("参数错误", "铜带宽度必须大于0")
            return False
        
        if coil['count'] <= 0:
            messagebox.showerror("参数错误", "铜带根数必须大于0")
            return False
        
        if coil['turns'] <= 0:
            messagebox.showerror("参数错误", "匝数必须大于0")
            return False
        
        if coil['inner_length'] <= 0:
            messagebox.showerror("参数错误", "复绕内径长度必须大于0")
            return False
        
        if coil['inner_height'] <= 0:
            messagebox.showerror("参数错误", "复绕内径高度必须大于0")
            return False
        
        # 检查模具参数
        if mold['length_limit'] <= 0:
            messagebox.showerror("参数错误", "模具长度限制必须大于0")
            return False
        
        if mold['height_limit'] <= 0:
            messagebox.showerror("参数错误", "模具高度限制必须大于0")
            return False
        
        if mold['width_limit'] <= 0:
            messagebox.showerror("参数错误", "模具宽度限制必须大于0")
            return False
        
        # 检查逻辑关系
        if coil['inner_length'] >= mold['length_limit']:
            messagebox.showwarning("参数警告", "复绕内径长度接近或超过模具长度限制")
        
        if coil['inner_height'] >= mold['height_limit']:
            messagebox.showwarning("参数警告", "复绕内径高度接近或超过模具高度限制")
        
        if coil['width'] >= mold['width_limit']:
            messagebox.showwarning("参数警告", "铜带宽度接近或超过模具宽度限制")
        
        return True
    
    def reset_parameters(self):
        """重置参数到默认值"""
        self.coil_vars['thickness'].set(1.0)       # 1mm铜带厚度（按用户要求）
        self.coil_vars['width'].set(50.0)          # 铜带宽度
        self.coil_vars['count'].set(1)             # 1根铜带
        self.coil_vars['turns'].set(3)             # 3匝
        self.coil_vars['inner_length'].set(200.0)  # 内径长度200mm
        self.coil_vars['inner_height'].set(85.0)   # 内径高度85mm

        self.mold_vars['length_limit'].set(300.0)  # 模具尺寸要大一些
        self.mold_vars['height_limit'].set(200.0)
        self.mold_vars['width_limit'].set(80.0)
    
    def save_parameters(self):
        """保存参数到文件"""
        try:
            from tkinter import filedialog
            import json
            
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="保存参数文件"
            )
            
            if filename:
                params = self.get_parameters()
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(params, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("保存成功", f"参数已保存到 {filename}")
        
        except Exception as e:
            messagebox.showerror("保存失败", f"保存参数时出错：{str(e)}")
    
    def load_parameters(self):
        """从文件加载参数"""
        try:
            from tkinter import filedialog
            import json
            
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="加载参数文件"
            )
            
            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    params = json.load(f)
                
                # 设置铜带参数
                coil = params.get('coil', {})
                for key, var in self.coil_vars.items():
                    if key in coil:
                        var.set(coil[key])
                
                # 设置模具参数
                mold = params.get('mold', {})
                for key, var in self.mold_vars.items():
                    if key in mold:
                        var.set(mold[key])
                
                messagebox.showinfo("加载成功", f"参数已从 {filename} 加载")
        
        except Exception as e:
            messagebox.showerror("加载失败", f"加载参数时出错：{str(e)}")
    
    def set_calculate_callback(self, callback: Callable):
        """设置计算回调函数"""
        self.on_parameter_change = callback

    def get_frame(self):
        """获取主框架"""
        return self.main_frame
